from datetime import datetime, timed<PERSON><PERSON>

def get_date(offset=15):
  return (datetime.now() + timedelta(days=offset)).strftime("%Y%m%d")

def is_accommodation_search_response(request):
  return 'accommodationSearchQuery' in request.url

def is_accommodation_deals_response(request):
  return 'accommodationDealsQuery' in request.url

def is_advertiser_details_response(request):
  return 'getAdvertiserDetails' in request.url

def extract_hotels_from_response(resp_json):
  hotels = []
  accommodations = resp_json.get("data", {}).get("accommodationSearchResponse", {}).get("accommodations", [])
  for acc in accommodations:
    hotels.append({
      "id": str(acc["nsid"]["id"]),
      "name": acc["accommodationDetails"]["translatedName"]["value"],
      # "coordinates": {
      #   "latitude": acc["coordinates"]["latitude"],
      #   "longitude": acc["coordinates"]["longitude"]
      # }
    })
  # if len(hotels) > 0:
    # print(f"Extracted {len(hotels)} hotels")

  return hotels

def extract_companies_from_response(resp_json):
  companies = []
  details = resp_json.get("data", {}).get("getAdvertiserDetails", {}).get("advertiserDetails", [])
  for det in details:
    companies.append({
      "id": str(det["nsid"]["id"]),
      "name": det["translatedName"]["value"]
    })

  return companies

def extract_prices_from_response(resp_json):
  prices = []
  deals = resp_json.get("data", {}).get("getAccommodationDeals", {}).get("deals", [])
  for deal in deals:
    prices.append({
      "hotelId": str(deal["accommodationDetails"]["nsid"]["id"]),
      "companyId": str(deal["advertiserDetails"]["nsid"]["id"]),
      "price": deal["pricePerNight"]["amount"]
    })
  return prices

def aggregate_hotels_prices(hotels, companies, prices):
  # hotel_map = {hotel['id']: {'name': hotel['name'], 'coordinates': hotel['coordinates']} for hotel in hotels}
  hotel_map = {hotel['id']: {'name': hotel['name']} for hotel in hotels}
  company_map = {company['id']: company['name'] for company in companies}

  hotels_prices = []
  hotels_prices_map = {}

  for price in prices:
    hotel_id = price['hotelId']

    if hotel_id not in hotels_prices_map:
      hotels_prices_map[hotel_id] = {
        'trivagoId': str(hotel_id),
        'hotelName': hotel_map[hotel_id]['name'] if hotel_id in hotel_map else None,
        # 'coordinates': hotel_map[hotel_id]['coordinates'] if hotel_id in hotel_map else None,
        'prices': []
      }
      hotels_prices.append(hotels_prices_map[hotel_id])

    hotels_prices_map[hotel_id]['prices'].append({
      'companyName': company_map.get(price['companyId'], None),
      'companyPrice': price['price']
    })

  return hotels_prices