import time
import gzip
import json
from app.utils.helpers import extract_companies_from_response, extract_hotels_from_response, extract_prices_from_response, is_accommodation_deals_response, is_accommodation_search_response, is_advertiser_details_response

from seleniumwire import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriver<PERSON><PERSON>
from selenium.webdriver.support import expected_conditions as EC
import random

def get_random_user_agent():
  user_agents = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.3',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.3',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/124.0.0.',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:125.0) Gecko/20100101 Firefox/125.',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.3',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36 Edg/117.0.2045.4',
    'Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 Edg/122.0.0.',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/123.0.0.',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:126.0) Gecko/20100101 Firefox/126.',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/109.0.0.',
    'Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Geck',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.3',
    'Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36 OPR/95.0.0.',
    'Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36 Edg/109.0.1518.10',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/118.',
    'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.0.0 Safari/537.3',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.3',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.3',
    'Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36 Edg/109.0.1518.14',
  ]
  
  return random.choice(user_agents)

def setup_browser():
  chrome_options = Options()
  chrome_options.add_argument("--headless")
  chrome_options.add_argument("--window-size=1920,1080")
  chrome_options.add_argument("--no-sandbox")
  chrome_options.add_argument("--disable-gpu")
  chrome_options.add_argument("--disable-dev-shm-usage")
  chrome_options.add_argument(f'user-agent={get_random_user_agent()}')
  chrome_options.add_experimental_option("prefs", {"profile.managed_default_content_settings.images": 2})

  driver = webdriver.Chrome(options=chrome_options)
  return driver

def search_destination(driver, destination):
  del driver.requests

  if driver.current_url != "https://www.trivago.com.br/":
    driver.get("https://www.trivago.com.br/")
  
  search_button = driver.find_element(By.CSS_SELECTOR, 'input[data-testid*="search-form-destination"]')
  driver.execute_script("arguments[0].click();", search_button)
  search_button.send_keys(destination)
  
  suggestions = []
  try:
    driver.wait_for_request("GetSearchSuggestions", timeout=10)
  except Exception as e:
    print(f"Error waiting for search suggestions: {e}")

  for req in driver.requests:
    if req.response and "GetSearchSuggestions" in req.url:
      try:
        resp_body = req.response.body
        if req.response.headers.get('Content-Encoding') == 'gzip':
          resp_body = gzip.decompress(resp_body)
        resp_json = json.loads(resp_body.decode('utf-8'))
        s_data = resp_json.get('data', {}).get('getSearchSuggestions', {}).get('unifiedSearchSuggestions', [])
        search_button.clear()

        for s in s_data:
          print(s)

          suggestions.append({
            "id": str(s["concept"]["nsid"]["id"]),
            "name": s["concept"]["translatedName"]["value"],
            "slug": s["concept"]["translatedName"]["value"].lower().replace(" ", "-"),
            "locationType": s["concept"]["typeObject"]["translatedName"]["value"],
            "location": s.get("locationLabel", "")
          })
        break
      except Exception as e:
        print(e)
        print(f"Error processing request: {e}")

  return suggestions

def click_first_more_deals_button(driver):
  try:
    WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.CSS_SELECTOR, 'button[data-testid*="additional-prices-slideout-entry-point"]')))
    more_deals_button = driver.find_element(By.CSS_SELECTOR, 'button[data-testid*="additional-prices-slideout-entry-point"]')
    driver.execute_script("arguments[0].click();", more_deals_button)
  except Exception as e:
    pass

def click_more_deals_buttons(driver):
  try:
    WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.CSS_SELECTOR, 'button[data-testid*="additional-prices-slideout-entry-point"]')))
    more_deals_buttons = driver.find_elements(By.CSS_SELECTOR, 'button[data-testid*="additional-prices-slideout-entry-point"]')
    for btn in more_deals_buttons:
      try:
        driver.execute_script("arguments[0].click();", btn)
      except Exception as e:
        pass
  except Exception as e:
    pass

def is_last_page(driver):
  try:
    next_page_btn = driver.find_element(By.CSS_SELECTOR, 'button[data-testid*="next-result-page"]')
    if next_page_btn:
      return False
  except:
    pass
  return True

def process_driver_requests(driver):
  hotels, companies, prices = [], [], []
  for req in driver.requests:
    if req.response and req.response.status_code == 200:
      try:
        if "graphql" in req.url:
          resp_body = req.response.body
          if req.response.headers.get('Content-Encoding') == 'gzip':
            resp_body = gzip.decompress(resp_body)
          resp_json = json.loads(resp_body.decode('utf-8'))

          if is_accommodation_search_response(req):
            page_hotels = extract_hotels_from_response(resp_json)
            hotels.extend(page_hotels)
          elif is_advertiser_details_response(req):
            page_companies = extract_companies_from_response(resp_json)
            companies.extend(page_companies)
          elif is_accommodation_deals_response(req):
            page_prices = extract_prices_from_response(resp_json)
            prices.extend(page_prices)
      except Exception as e:
        print(f"Error processing request: {e}")
  return hotels, companies, prices

def get_page_info(driver, url):
  start = time.time()

  del driver.requests
  try:
    driver.get(url)
  except Exception as e:
    pass

  click_more_deals_buttons(driver)
  last = is_last_page(driver)
  hotels, companies, prices = process_driver_requests(driver)

  metrics = {
    "hotelsCount": len(hotels),
    "executionTime": time.time() - start,
    "hotels": hotels
  }

  return {"hotels": hotels, "companies": companies, "prices": prices}, metrics, last

def get_hotel_info(driver, url):
  del driver.requests
  driver.get(url)

  click_first_more_deals_button(driver)
  time.sleep(2)

  hotels, companies, prices = process_driver_requests(driver)

  return {"hotels": hotels, "companies": companies, "prices": prices}
